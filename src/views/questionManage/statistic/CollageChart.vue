<template>
  <div class="ml-20px mr-20px">
    <switchTab lbl="学生类别" :tabs="xslbArr" onlyName="fullName" onlyKey="enCode" @changeTabItem="item => changeTabItem(item, 'nj')"></switchTab>
    <switchTab lbl="现在年级" :tabs="njArr" onlyName="njmc" onlyKey="nj" @changeTabItem="item => changeTabItem(item, 'nj')"></switchTab>
    <div class="con-box">
      <BasicTable @register="registerTable"> </BasicTable>
      <eChart ref="eChartRef" :options="eChartObj.options" :height="450" @reload=""></eChart>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, reactive, ref } from 'vue';
  import { useBaseStore } from '@/store/modules/base';
  import { BasicTable, useTable } from '@/components/Table';
  import * as schoolApi from '@/api/school';
  import switchTab from './components/switchTab.vue';
  import eChart from '@/views/questionManage/statistic/components/chart.vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const props = defineProps(['wjdm']);
  const api = useBaseApi('/api/knsDcwj');

  const baseStore = useBaseStore();
  const njArr = ref([]);
  const xslbArr = ref([]);
  const xyArr = ref([]);
  const columns = [
    { title: '院系名称', dataIndex: 'dwmc' },
    { title: '已参加人数', dataIndex: 'examineCount', width: 130, align: 'right' },
    { title: '未参加人数', dataIndex: 'notExamineCount', width: 130, align: 'right' },
    { title: '总人数', dataIndex: 'allCount', width: 80, align: 'right', fixed: 'right' },
  ];
  const [registerTable, { reload, setTableData }] = useTable({
    api: params => api.request('get', '/listDwStatistics', { params }),
    beforeFetch: params => {
      const data = {
        ...params,
        ...query,
        wjdm: props.wjdm;
      }
        return data
      },
    afterFetch: ({ data }) => {
      setTimeout(() => {
        getOptions();
      }, 100);
      return data;
    },
    columns,
    immediate: false,
    useSearchForm: false,
    clickToRowSelect: false,
    pagination: false,
    showTableSetting: false,
  });

  const ALL_OPTION = {
    nj: '',
    njmc: '全部',
    enCode: '',
    fullName: '全部',
  };

  async function getOptions() {
    try {
      const { data: xyList } = await schoolApi.getXY({ pageSize: 99999 });
      const { data: nrList } = await schoolApi.getCollegeGradeInfo({ pageSize: 99999 });
      const { data: dictList } = await baseStore.getDictionaryData('xslb');

      xyArr.value = xyList;
      njArr.value = [
        ALL_OPTION,
        ...nrList.map(item => ({
          nj: item,
          njmc: item,
        })),
      ];

      xslbArr.value = [ALL_OPTION, ...(dictList as any[])];
    } catch (error) {
      console.error('Failed to load options:', error);
    }
  }
  const query = reactive({
    nj: '',
  });
  async function changeTabItem(item, key) {
    if (item != '') query[key] = item;
    reload(query);
  }
  const eChartObj = reactive({
    options: {
      legend: {
        data: ['未参加', '已参加'],
        orient: 'horizontal',
        top: 'top',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '5%',
        right: '20%',
        bottom: '0%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
        axisLabel: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: 'category',
        splitLine: {
          show: false,
        },
        data: [
          '金融学院',
          '计算机学院',
          '外语学院',
          '数学与统计学院',
          '物理学院',
          '化学学院',
          '生物学院',
          '材料学院',
          '机械学院',
          '电气学院',
          '土木学院',
          '水利学院',
        ],
      },
      series: [
        {
          name: '未参加',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            position: 'insideRight',
          },
          data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
          itemStyle: {
            color: '#F5A12F',
          },
        },
        {
          name: '已参加',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            position: 'insideRight',
          },
          data: [180, 17, 106, 15, 0, 130, 12, 11, 10, 90, 8],
          itemStyle: {
            color: '#28D988',
          },
        },
      ],
    },
  });
  onMounted(() => {
    getOptions();
    reload();
  });
</script>
<style scoped lang="less">
  .con-box {
    display: grid;
    grid-template-columns: 40% 60%;
    // padding: 15px 10px 0px 10px;
    .active {
      color: @primary-color;
      cursor: pointer;
    }
    .chart-container {
      width: 100%;
      height: 100%;
      padding: 10px 0;
      background-color: white;
    }
  }
</style>
